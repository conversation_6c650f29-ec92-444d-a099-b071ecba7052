pub mod template_repository;
pub mod generated_document_repository; // Adicionado novo repositório
pub mod local_client_supplement_repository; // Adicionado novo repositório
pub mod recharge_package_repository;
pub mod recharge_code_repository;
pub mod recharge_sale_repository;

use mongodb::{Client, Database};
use crate::config::AppConfig;
use crate::error::Result;

#[derive(Clone)]
pub struct DatabaseManager {
    pub client: Client,
    pub database: Database,
}

impl DatabaseManager {
    pub async fn new(config: &AppConfig) -> Result<Self> {
        let client = Client::with_uri_str(&config.mongodb_uri).await?;
        let database = client.database(&config.mongodb_database);
        
        // Teste a conexão
        client
            .database("admin")
            .run_command(bson::doc! {"ismaster": 1})
            .await?;
        
        tracing::info!("Conectado ao MongoDB com sucesso");
        
        Ok(Self { client, database })
    }
}
