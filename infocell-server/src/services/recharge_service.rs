use crate::{
    error::{AppError, Result},
    models::{RechargeCode, RechargeSale},
    repositories::{
        recharge_code_repository::RechargeCodeRepository,
        recharge_package_repository::RechargePackageRepository,
        recharge_sale_repository::RechargeSaleRepository,
        DatabaseManager,
    },
    handlers::recharge_handlers::ImportRechargeCodesResponse,
};
use chrono::{DateTime, Utc};
use std::collections::HashSet;

#[derive(Clone)]
pub struct RechargeService {
    db_manager: DatabaseManager,
    code_repo: RechargeCodeRepository,
    package_repo: RechargePackageRepository,
    sale_repo: RechargeSaleRepository,
}

impl RechargeService {
    pub fn new(
        db_manager: DatabaseManager,
        code_repo: RechargeCodeRepository,
        sale_repo: RechargeSaleRepository,
        package_repo: RechargePackageRepository,
    ) -> Self {
        Self {
            db_manager,
            code_repo,
            package_repo,
            sale_repo,
        }
    }

    /// Valida se um código de recarga tem o formato correto
    ///
    /// # Argumentos
    /// * `code` - O código a ser validado
    ///
    /// # Retorna
    /// * `Ok(())` - Se o código é válido
    /// * `Err(String)` - Se o código é inválido, com a descrição do erro
    fn validate_recharge_code(code: &str) -> std::result::Result<(), String> {
        // Verificar se o código tem exatamente 16 caracteres
        if code.len() != 16 {
            return Err(format!("Código '{}' deve ter exatamente 16 dígitos (atual: {})", code, code.len()));
        }

        // Verificar se contém apenas dígitos
        if !code.chars().all(|c| c.is_ascii_digit()) {
            return Err(format!("Código '{}' deve conter apenas dígitos", code));
        }

        Ok(())
    }

    /// Valida uma lista de códigos e retorna códigos válidos e erros
    ///
    /// # Argumentos
    /// * `codes` - Lista de códigos a serem validados
    ///
    /// # Retorna
    /// * `(valid_codes, errors)` - Tupla com códigos válidos e lista de erros
    fn validate_codes_batch(codes: &[String]) -> (Vec<String>, Vec<String>) {
        let mut valid_codes = Vec::new();
        let mut errors = Vec::new();
        let mut seen_codes = HashSet::new();

        for code in codes {
            let trimmed_code = code.trim().to_string();

            // Verificar se o código não está vazio
            if trimmed_code.is_empty() {
                continue; // Ignorar códigos vazios silenciosamente
            }

            // Verificar duplicatas na mesma requisição
            if seen_codes.contains(&trimmed_code) {
                errors.push(format!("Código '{}' está duplicado na requisição", trimmed_code));
                continue;
            }
            seen_codes.insert(trimmed_code.clone());

            // Validar formato do código
            match Self::validate_recharge_code(&trimmed_code) {
                Ok(()) => valid_codes.push(trimmed_code),
                Err(error) => errors.push(error),
            }
        }

        (valid_codes, errors)
    }

    pub async fn import_recharge_codes(
        &self,
        codes: Vec<String>,
        package_type: String,
        unit_cost: f64,
        erp_supplier_id: String,
        validity_days: u32,
        imported_by_user_id: String,
    ) -> Result<ImportRechargeCodesResponse> {
        let total_processed = codes.len() as u32;
        let mut errors = Vec::new();

        // 1. Validação do Pacote
        let package_exists = self.package_repo.find_by_package_type(&package_type).await?.is_some();
        if !package_exists {
            return Err(AppError::ValidationError(format!(
                "Tipo de pacote '{}' não existe ou não está ativo.",
                package_type
            )));
        }

        // 2. Validação de formato e duplicatas na própria requisição
        let (valid_codes, validation_errors) = Self::validate_codes_batch(&codes);
        errors.extend(validation_errors);

        // Se houver erros de formato ou duplicatas na requisição, parar aqui.
        if !errors.is_empty() {
            return Ok(ImportRechargeCodesResponse {
                success: false,
                imported: 0,
                failed: total_processed,
                errors,
                total_processed,
            });
        }

        // 3. Validação de duplicatas contra o banco de dados
        let existing_codes = self.code_repo.find_existing_codes(&valid_codes).await?;

        if !existing_codes.is_empty() {
            for code in existing_codes {
                errors.push(format!("Código '{}' já existe no banco de dados.", code));
            }
            // Parar aqui se encontrarmos duplicatas no banco.
            return Ok(ImportRechargeCodesResponse {
                success: false,
                imported: 0,
                failed: total_processed,
                errors,
                total_processed,
            });
        }

        // 4. Se todas as validações passaram, proceder com a inserção
        let new_codes: Vec<RechargeCode> = valid_codes
            .into_iter() // Mover para `into_iter` para consumir os valores
            .map(|code| RechargeCode {
                id: None,
                code,
                package_type: package_type.clone(),
                unit_cost,
                erp_supplier_id: erp_supplier_id.clone(),
                validity_days,
                imported_at: Utc::now(),
                imported_by_user_id: imported_by_user_id.clone(),
                is_available: true,
                sale_id: None,
                sold_at: None,
            })
            .collect();

        self.code_repo.insert_many(new_codes).await?;

        // Se chegamos aqui, tudo deu certo.
        Ok(ImportRechargeCodesResponse {
            success: true,
            imported: total_processed,
            failed: 0,
            errors: vec![],
            total_processed,
        })
    }

    pub async fn get_recharge_stock(
        &self,
        page: u32,
        per_page: u32,
        package_type: Option<String>,
        is_available: Option<bool>,
    ) -> Result<(Vec<RechargeCode>, u64)> {
        self.code_repo
            .find_all(page, per_page, package_type, is_available)
            .await
    }

    pub async fn perform_recharge_sale(
        &self,
        erp_client_id: String,
        erp_employee_id: String,
        package_type: String,
        payment_method: String,
        quantity: u32,
    ) -> Result<(RechargeSale, Vec<RechargeCode>)> {
        let mut session = self
            .db_manager
            .client
            .start_session()
            .await
            .map_err(AppError::DatabaseError)?;

        session.start_transaction().await.map_err(AppError::DatabaseError)?;

        let transaction_result: Result<(RechargeSale, Vec<RechargeCode>)> = async {
            // 1. Encontrar pacote para validar preço e existência
            let package = self.package_repo.find_by_package_type(&package_type).await?
                .ok_or_else(|| AppError::NotFound(format!("Pacote ativo do tipo '{}' não encontrado.", package_type)))?;

            // 2. Criar a venda primeiro para obter o sale_id
            let total_sale_price = package.sale_price * f64::from(quantity);

            let mut sale = RechargeSale {
                id: None,
                erp_client_id,
                erp_employee_id,
                package_type: package.package_type,
                quantity,
                recharge_code_ids: Vec::new(), // Será preenchido após encontrar os códigos
                total_sale_price,
                payment_method,
                sale_timestamp: Utc::now(),
            };
            let sale_id = self.sale_repo.create(&sale).await?;
            sale.id = Some(sale_id.clone());

            // 3. Encontrar e marcar códigos como vendidos atomicamente
            let mut sold_codes = Vec::new();
            let mut recharge_code_ids = Vec::new();

            for _ in 0..quantity {
                match self.code_repo.find_and_mark_one_as_sold(&package_type, &sale_id).await? {
                    Some(code) => {
                        recharge_code_ids.push(code.id.unwrap());
                        sold_codes.push(code);
                    }
                    None => {
                        // Estoque insuficiente - a transação será abortada
                        return Err(AppError::BadRequest(format!(
                            "Estoque insuficiente. Apenas {} códigos disponíveis de {} solicitados.",
                            sold_codes.len(),
                            quantity
                        )));
                    }
                }
            }

            // 4. Atualizar a venda com os IDs dos códigos vendidos
            sale.recharge_code_ids = recharge_code_ids;

            Ok((sale, sold_codes))
        }.await;

        match transaction_result {
            Ok((sale, sold_codes)) => {
                session.commit_transaction().await.map_err(AppError::DatabaseError)?;
                Ok((sale, sold_codes))
            }
            Err(e) => {
                session.abort_transaction().await.map_err(AppError::DatabaseError)?;
                Err(e)
            }
        }
    }

    pub async fn get_sales_report(
        &self,
        page: u32,
        per_page: u32,
        erp_client_id: Option<String>,
        erp_employee_id: Option<String>,
        start_date: Option<DateTime<Utc>>,
        end_date: Option<DateTime<Utc>>,
    ) -> Result<(Vec<RechargeSale>, u64)> {
        self.sale_repo
            .find_all(
                page,
                per_page,
                erp_client_id,
                erp_employee_id,
                start_date,
                end_date,
            )
            .await
    }
} 