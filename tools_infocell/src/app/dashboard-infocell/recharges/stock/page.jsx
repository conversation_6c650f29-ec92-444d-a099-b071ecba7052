"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import infocellApi from '@/lib/infocellApi';
import { useDebounce } from '@/lib/hooks/useDebounce'; // Supondo que você tenha um hook de debounce

export default function RechargeStockPage() {
    const [stock, setStock] = useState([]);
    const [packages, setPackages] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({ page: 1, per_page: 20, total: 0 });
    const [filters, setFilters] = useState({
        package_type: 'all',
        is_available: 'all',
    });

    const debouncedFilters = useDebounce(filters, 500);

    const fetchStock = useCallback(async (page = 1) => {
        setIsLoading(true);
        setError(null);
        try {
            const apiParams = { page, per_page: pagination.per_page };
            if (debouncedFilters.package_type !== 'all') {
                apiParams.package_type = debouncedFilters.package_type;
            }
            if (debouncedFilters.is_available !== 'all') {
                apiParams.is_available = debouncedFilters.is_available === 'true';
            }
            
            const response = await infocellApi.getRechargeStock(apiParams);
            setStock(response.data.codes || []);
            setPagination(prev => ({ ...prev, total: response.data.total, page }));
        } catch (err) {
            setError('Falha ao buscar estoque.');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    }, [pagination.per_page, debouncedFilters]);

    useEffect(() => {
        fetchStock(1);
    }, [fetchStock]);

    useEffect(() => {
        const fetchPackages = async () => {
            try {
                const response = await infocellApi.getRechargePackages();
                setPackages(response.data || []);
            } catch (err) {
                console.error("Failed to fetch packages for filter");
            }
        };
        fetchPackages();
    }, []);
    
    const handleFilterChange = (name, value) => {
        setFilters(prev => ({ ...prev, [name]: value }));
    };

    const handlePageChange = (newPage) => {
        if (newPage > 0 && newPage <= Math.ceil(pagination.total / pagination.per_page)) {
            fetchStock(newPage);
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('pt-BR');
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Estoque de Códigos de Recarga</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="flex space-x-4 mb-4">
                    <Select onValueChange={(value) => handleFilterChange('package_type', value)} defaultValue="all">
                        <SelectTrigger>
                            <SelectValue placeholder="Filtrar por pacote" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos os Pacotes</SelectItem>
                            {packages.map(p => <SelectItem key={p._id} value={p.package_type}>{p.package_name}</SelectItem>)}
                        </SelectContent>
                    </Select>
                    <Select onValueChange={(value) => handleFilterChange('is_available', value)} defaultValue="all">
                        <SelectTrigger>
                            <SelectValue placeholder="Filtrar por status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos os Status</SelectItem>
                            <SelectItem value="true">Disponível</SelectItem>
                            <SelectItem value="false">Vendido</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Código</TableHead>
                                <TableHead>Tipo</TableHead>
                                <TableHead>Custo</TableHead>
                                <TableHead>Importado em</TableHead>
                                <TableHead>Status</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {isLoading ? (
                                <TableRow><TableCell colSpan="5" className="text-center">Carregando...</TableCell></TableRow>
                            ) : stock.map((item) => (
                                <TableRow key={item._id.$oid}>
                                    <TableCell className="font-mono">{item.code}</TableCell>
                                    <TableCell>{item.package_type}</TableCell>
                                    <TableCell>R$ {item.unit_cost.toFixed(2)}</TableCell>
                                    <TableCell>{formatDate(item.imported_at)}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.is_available ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                            {item.is_available ? 'Disponível' : 'Vendido'}
                                        </span>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
                
                <div className="flex items-center justify-between mt-4">
                    <span className="text-sm text-muted-foreground">
                        Total de {pagination.total} registros.
                    </span>
                    <div className="flex space-x-2">
                        <Button onClick={() => handlePageChange(pagination.page - 1)} disabled={pagination.page <= 1}>
                            Anterior
                        </Button>
                        <span className="p-2">{pagination.page} de {Math.ceil(pagination.total / pagination.per_page)}</span>
                        <Button onClick={() => handlePageChange(pagination.page + 1)} disabled={pagination.page >= Math.ceil(pagination.total / pagination.per_page)}>
                            Próximo
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
} 