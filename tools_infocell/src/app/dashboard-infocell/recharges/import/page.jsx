"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import SupplierSearchInput from '@/components/custom/SupplierSearchInput';
import infocellApi from '@/lib/infocellApi';
import { toast } from 'sonner';

export default function ImportRechargesPage() {
    const [formData, setFormData] = useState({
        codes: '',
        package_type: '',
        unit_cost: '',
        validity_days: 30,
    });
    const [selectedSupplier, setSelectedSupplier] = useState(null);
    const [activePackages, setActivePackages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [importResult, setImportResult] = useState(null);

    useEffect(() => {
        const fetchPackages = async () => {
            setIsLoading(true);
            try {
                const packagesResponse = await infocellApi.getRechargePackages();
                setActivePackages(packagesResponse.data.filter(p => p.is_active));
            } catch (err) {
                toast.error(`Falha ao buscar pacotes: ${err.message}`);
            } finally {
                setIsLoading(false);
            }
        };
        fetchPackages();
    }, []);
    
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };
    
    const handleSelectChange = (name, value) => {
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const resetForm = () => {
        setFormData({
            codes: '', package_type: '', unit_cost: '', validity_days: 30,
        });
        setSelectedSupplier(null);
        setImportResult(null);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        const codesArray = formData.codes.split('\n').map(c => c.trim()).filter(c => c);

        if (codesArray.length === 0 || !selectedSupplier) {
            toast.error("Por favor, preencha todos os campos, incluindo o fornecedor.");
            return;
        }

        setIsLoading(true);
        const payload = {
            codes: codesArray,
            package_type: formData.package_type,
            unit_cost: parseFloat(formData.unit_cost),
            erp_supplier_id: selectedSupplier.id,
            validity_days: parseInt(formData.validity_days, 10),
        };

        try {
            const response = await infocellApi.importRechargeCodes(payload);
            const result = response;

            setImportResult(result);

            if (result.success && result.imported > 0) {
                if (result.failed === 0) {
                    toast.success(`Todos os ${result.imported} códigos foram importados com sucesso!`);
                } else {
                    toast.success(`${result.imported} códigos importados com sucesso. ${result.failed} falharam.`);
                }
            } else if (result.imported === 0) {
                toast.error(`Nenhum código foi importado. ${result.failed} códigos falharam.`);
            } else {
                toast.warning(`Importação parcial: ${result.imported} sucessos, ${result.failed} falhas.`);
            }

            // Não resetar o form automaticamente para que o usuário possa ver os resultados
        } catch (err) {
            toast.error(`Falha ao importar lote: ${err.message}`);
            setImportResult(null);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Importar Lote de Recargas</CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                        <Label htmlFor="codes">Códigos de Recarga (um por linha)</Label>
                        <Textarea id="codes" name="codes" value={formData.codes} onChange={handleInputChange} required rows={10} placeholder="Cole os códigos aqui..."/>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <Label htmlFor="package_type">Tipo de Pacote</Label>
                            <Select onValueChange={(value) => handleSelectChange('package_type', value)} value={formData.package_type} required>
                                <SelectTrigger>
                                    <SelectValue placeholder="Selecione o pacote" />
                                </SelectTrigger>
                                <SelectContent>
                                    {activePackages.map(pkg => (
                                        <SelectItem key={pkg._id} value={pkg.package_type}>{pkg.package_name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Label>Fornecedor</Label>
                            <SupplierSearchInput onSelect={setSelectedSupplier} selectedSupplier={selectedSupplier} />
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                       <div>
                            <Label htmlFor="unit_cost">Custo Unitário (R$)</Label>
                            <Input id="unit_cost" name="unit_cost" type="number" step="0.01" value={formData.unit_cost} onChange={handleInputChange} required placeholder="Ex: 15.50"/>
                        </div>
                        <div>
                            <Label htmlFor="validity_days">Dias de Validade</Label>
                            <Input id="validity_days" name="validity_days" type="number" value={formData.validity_days} onChange={handleInputChange} required />
                        </div>
                    </div>
                    
                    <Button type="submit" disabled={isLoading || !selectedSupplier}>
                        {isLoading ? 'Importando...' : 'Importar Lote'}
                    </Button>
                </form>

                {/* Resultado da Importação */}
                {importResult && (
                    <div className="mt-6 space-y-4">
                        <div className="border-t pt-4">
                            <h3 className="text-lg font-semibold mb-3">Resultado da Importação</h3>

                            {/* Resumo */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                    <div className="text-2xl font-bold text-blue-600">{importResult.total_processed}</div>
                                    <div className="text-sm text-blue-800">Total Processados</div>
                                </div>
                                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                                    <div className="text-2xl font-bold text-green-600">{importResult.imported}</div>
                                    <div className="text-sm text-green-800">Importados com Sucesso</div>
                                </div>
                                <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                                    <div className="text-2xl font-bold text-red-600">{importResult.failed}</div>
                                    <div className="text-sm text-red-800">Falharam</div>
                                </div>
                            </div>

                            {/* Lista de Erros */}
                            {importResult.errors && importResult.errors.length > 0 && (
                                <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                                    <h4 className="font-semibold text-red-800 mb-2">Erros Encontrados:</h4>
                                    <ul className="list-disc list-inside space-y-1 text-sm text-red-700">
                                        {importResult.errors.map((error, index) => (
                                            <li key={index}>{error}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}

                            {/* Botão para nova importação */}
                            <div className="flex justify-end mt-4">
                                <Button onClick={resetForm} variant="outline">
                                    Nova Importação
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}